# 航材共享保障平台数据管理方案

**项目名称**: 航材共享保障平台  
**制定时间**: 2025年1月15日  
**目标**: 建立完整的数据生命周期管理体系  

---

## 📊 数据分类体系

### 1. 基础配置数据（生产环境必须保留）

#### 系统配置
```python
# 用户角色定义
USER_ROLES = {
    'airline': '航空公司',
    'supplier': '供应商', 
    'maintenance': '维修企业'
}

# 系统参数配置
SYSTEM_CONFIG = {
    'aog_response_time_limit': 120,  # AOG响应时间限制（分钟）
    'default_currency': 'CNY',       # 默认货币
    'items_per_page': 20,           # 分页大小
    'max_items_per_page': 100       # 最大分页大小
}
```

#### 业务基础数据
```python
# 航材分类体系
MATERIAL_CATEGORIES = [
    '发动机及部件',
    '起落架系统', 
    '航电设备',
    '液压系统',
    '燃油系统',
    '空调系统',
    '机体结构',
    '其他'
]

# 地区代码表
REGION_CODES = {
    'BJS': '北京',
    'SHA': '上海', 
    'GUA': '广州',
    'SHE': '沈阳',
    'CHE': '成都'
}

# 货币汇率基础数据
CURRENCY_RATES = {
    'CNY': 1.0,
    'USD': 7.2,
    'EUR': 7.8
}
```

#### 工作流模板
```python
# 审批流程模板
WORKFLOW_TEMPLATES = {
    'simple_approval': '简单审批流（一级审批）',
    'multi_level_approval': '多级审批流（部门→财务→总经理）',
    'parallel_approval': '并行审批流（多部门同时审批）'
}
```

### 2. 演示数据（可选择性保留）

#### 演示用户账号
```python
DEMO_USERS = [
    {
        'username': 'airline_demo',
        'email': '<EMAIL>',
        'user_type': 'airline',
        'company_name': '中国国际航空公司',
        'phone': '010-12345678'
    },
    {
        'username': 'supplier_demo', 
        'email': '<EMAIL>',
        'user_type': 'supplier',
        'company_name': '中航材集团',
        'phone': '010-87654321'
    },
    {
        'username': 'maintenance_demo',
        'email': '<EMAIL>', 
        'user_type': 'maintenance',
        'company_name': '海航技术',
        'phone': '0898-12345678'
    }
]
```

#### 典型航材信息
```python
DEMO_MATERIALS = [
    {
        'part_number': 'CFM56-7B24',
        'part_name': 'CFM56发动机',
        'category': '发动机及部件',
        'manufacturer': 'CFM International',
        'aircraft_type': 'B737-800',
        'unit_price': 12000000.00
    },
    {
        'part_number': 'A320-WHL-001',
        'part_name': '主起落架机轮',
        'category': '起落架系统', 
        'manufacturer': 'Safran Landing Systems',
        'aircraft_type': 'A320',
        'unit_price': 45000.00
    }
    # ... 更多演示数据
]
```

#### 演示订单数据
```python
DEMO_ORDERS = [
    {
        'order_number': 'ORD-2025-001',
        'material_id': 1,
        'quantity': 1,
        'status': 'completed',
        'priority': 'normal',
        'total_amount': 12000000.00
    },
    {
        'order_number': 'ORD-2025-002', 
        'material_id': 2,
        'quantity': 2,
        'status': 'processing',
        'priority': 'aog',
        'total_amount': 90000.00
    }
    # ... 更多演示订单
]
```

### 3. 测试数据（项目完成时需要清理）

#### 测试用户账号
- 大量随机生成的测试用户
- 各种边界情况的用户数据
- 调试过程中创建的临时账号

#### 测试业务数据
- 随机生成的航材信息
- 测试订单和交易记录
- 调试日志和临时文件
- 性能测试数据

---

## 🔧 技术实现方案

### 数据管理工具类

```python
# utils/data_manager.py
from datetime import datetime
from models import db, User, Material, Order, Inventory

class DataManager:
    """数据管理工具类"""
    
    @staticmethod
    def init_base_data():
        """初始化基础配置数据 - 生产环境必需"""
        print("正在初始化基础配置数据...")
        
        # 创建系统配置
        # 创建用户角色
        # 创建航材分类
        # 创建地区代码
        
        db.session.commit()
        print("基础配置数据初始化完成")
        
    @staticmethod  
    def init_demo_data():
        """初始化演示数据 - 用于系统展示"""
        print("正在初始化演示数据...")
        
        # 创建演示用户
        for user_data in DEMO_USERS:
            user = User(**user_data)
            db.session.add(user)
            
        # 创建演示航材
        for material_data in DEMO_MATERIALS:
            material = Material(**material_data)
            db.session.add(material)
            
        # 创建演示订单
        for order_data in DEMO_ORDERS:
            order = Order(**order_data)
            db.session.add(order)
            
        db.session.commit()
        print("演示数据初始化完成")
        
    @staticmethod
    def init_test_data():
        """初始化测试数据 - 仅开发使用"""
        print("正在初始化测试数据...")
        
        # 创建大量测试用户
        # 创建随机航材数据
        # 创建测试订单
        
        db.session.commit()
        print("测试数据初始化完成")
        
    @staticmethod
    def clean_test_data():
        """清理测试数据 - 部署前执行"""
        print("正在清理测试数据...")
        
        # 删除测试用户（保留演示用户）
        # 删除测试订单
        # 删除临时文件
        
        db.session.commit()
        print("测试数据清理完成")
        
    @staticmethod
    def export_production_data():
        """导出生产就绪的数据"""
        print("正在导出生产数据...")
        
        # 导出基础配置数据
        # 导出演示数据
        # 生成SQL脚本
        
        print("生产数据导出完成")
        
    @staticmethod
    def backup_database():
        """备份数据库"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_{timestamp}.db"
        
        # 执行数据库备份
        print(f"数据库备份完成: {backup_file}")
```

### 命令行管理工具

```python
# manage.py
import click
from utils.data_manager import DataManager

@click.group()
def cli():
    """航材共享保障平台数据管理工具"""
    pass

@cli.command()
@click.option('--mode', default='development', 
              type=click.Choice(['development', 'demo', 'production']),
              help='初始化模式')
def init_data(mode):
    """初始化数据"""
    if mode == 'development':
        DataManager.init_base_data()
        DataManager.init_demo_data() 
        DataManager.init_test_data()
    elif mode == 'demo':
        DataManager.init_base_data()
        DataManager.init_demo_data()
    elif mode == 'production':
        DataManager.init_base_data()

@cli.command()
@click.option('--type', default='test',
              type=click.Choice(['test', 'all']),
              help='清理数据类型')
def clean_data(type):
    """清理数据"""
    if type == 'test':
        DataManager.clean_test_data()
    elif type == 'all':
        click.confirm('确定要清理所有数据吗？', abort=True)
        # 清理所有数据

@cli.command()
def backup():
    """备份数据库"""
    DataManager.backup_database()

@cli.command()
def export():
    """导出生产数据"""
    DataManager.export_production_data()

if __name__ == '__main__':
    cli()
```

---

## 📋 不同阶段的数据处理

### 开发阶段（当前）
```bash
# 初始化完整开发数据
python manage.py init_data --mode=development

# 包含：基础配置 + 演示数据 + 测试数据
```

### 预生产阶段
```bash
# 清理测试数据
python manage.py clean_data --type=test

# 重新初始化演示环境
python manage.py init_data --mode=demo
```

### 生产部署
```bash
# 仅初始化基础数据
python manage.py init_data --mode=production

# 备份数据库
python manage.py backup

# 导出生产数据包
python manage.py export
```

---

## 🎯 数据质量保证

### 数据验证规则
- 所有演示数据必须是真实可信的
- 测试数据必须覆盖各种边界情况
- 基础配置数据必须经过业务验证

### 数据安全措施
- 敏感数据脱敏处理
- 测试数据定期清理
- 生产数据访问控制

### 数据监控
- 数据量监控
- 数据质量检查
- 异常数据告警

---

**状态**: 已制定，开始实施  
**更新时间**: 2025-01-15
