#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 航材管理API路由
版本: 1.0
创建时间: 2025-01-15

处理航材相关的所有API请求，包括搜索、创建、更新、删除等操作
"""

from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import and_, or_, desc

from models import db, Material, InventoryItem, User

# 创建蓝图
materials_bp = Blueprint('materials', __name__)

@materials_bp.route('/test', methods=['GET'])
@jwt_required()
def test_jwt():
    """测试JWT认证"""
    current_user_id = int(get_jwt_identity())  # 转换为整数
    return jsonify({
        'error': 0,
        'message': 'JWT认证成功',
        'body': {
            'user_id': current_user_id,
            'test': 'success'
        },
        'success': True
    })

@materials_bp.route('/search', methods=['GET'])
@jwt_required()
def search_materials():
    """
    航材搜索接口
    功能描述：根据关键词和筛选条件搜索航材
    入参：{ q: string, category: string, manufacturer: string, page: int, size: int }
    返回参数：分页的航材列表数据
    url地址：/api/v1/materials/search
    请求方式：GET
    """
    try:
        # 获取查询参数
        q = request.args.get('q', '').strip()
        category = request.args.get('category', '').strip()
        manufacturer = request.args.get('manufacturer', '').strip()
        aircraft_type = request.args.get('aircraft_type', '').strip()
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 验证分页参数
        if page < 1:
            page = 1
        if size < 1 or size > 100:
            size = 20
        
        # 构建查询
        query = Material.query
        
        # 关键词搜索
        if q:
            search_filter = or_(
                Material.part_number.ilike(f'%{q}%'),
                Material.part_name.ilike(f'%{q}%'),
                Material.description.ilike(f'%{q}%')
            )
            query = query.filter(search_filter)
        
        # 分类筛选
        if category:
            query = query.filter(Material.category == category)
        
        # 制造商筛选
        if manufacturer:
            query = query.filter(Material.manufacturer.ilike(f'%{manufacturer}%'))
        
        # 机型筛选
        if aircraft_type:
            query = query.filter(Material.aircraft_type.ilike(f'%{aircraft_type}%'))
        
        # 按创建时间降序排列
        query = query.order_by(desc(Material.created_at))
        
        # 分页查询
        pagination = query.paginate(page=page, per_page=size, error_out=False)
        
        # 获取每个航材的库存信息
        materials_data = []
        for material in pagination.items:
            material_dict = material.to_dict()
            
            # 获取库存统计
            inventory_stats = db.session.query(
                db.func.sum(InventoryItem.current_stock).label('total_stock'),
                db.func.count(InventoryItem.id).label('supplier_count')
            ).filter(
                InventoryItem.material_id == material.id,
                InventoryItem.current_stock > 0
            ).first()
            
            material_dict['inventory_stats'] = {
                'total_stock': inventory_stats.total_stock or 0,
                'supplier_count': inventory_stats.supplier_count or 0
            }
            
            materials_data.append(material_dict)
        
        return jsonify({
            'error': 0,
            'message': '航材搜索成功',
            'body': {
                'items': materials_data,
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            },
            'success': True
        })
        
    except Exception as e:
        return jsonify({
            'error': 500,
            'message': f'航材搜索失败: {str(e)}',
            'body': {},
            'success': False
        }), 500

@materials_bp.route('/', methods=['POST'])
@jwt_required()
def create_material():
    """
    创建航材接口
    功能描述：创建新的航材记录
    入参：{ part_number: string, part_name: string, category: string, ... }
    返回参数：创建的航材信息
    url地址：/api/v1/materials
    请求方式：POST
    """
    try:
        if not request.is_json:
            return jsonify({
                'error': 400,
                'message': '请求必须是JSON格式',
                'body': {},
                'success': False
            }), 400
        
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['part_number', 'part_name', 'category']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'error': 400,
                    'message': f'缺少必填字段: {field}',
                    'body': {},
                    'success': False
                }), 400
        
        # 检查件号是否已存在
        existing_material = Material.query.filter_by(part_number=data['part_number']).first()
        if existing_material:
            return jsonify({
                'error': 409,
                'message': '件号已存在',
                'body': {},
                'success': False
            }), 409
        
        # 创建航材
        material = Material(
            part_number=data['part_number'].strip(),
            part_name=data['part_name'].strip(),
            category=data['category'].strip(),
            manufacturer=data.get('manufacturer', '').strip(),
            aircraft_type=data.get('aircraft_type', '').strip(),
            description=data.get('description', '').strip(),
            status='active'
        )
        
        db.session.add(material)
        db.session.commit()
        
        return jsonify({
            'error': 0,
            'message': '航材创建成功',
            'body': material.to_dict(),
            'success': True
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': 500,
            'message': f'创建航材失败: {str(e)}',
            'body': {},
            'success': False
        }), 500

@materials_bp.route('/<int:material_id>', methods=['GET'])
@jwt_required()
def get_material(material_id):
    """
    获取航材详情接口
    功能描述：获取指定航材的详细信息
    入参：{ material_id: int }
    返回参数：航材详细信息
    url地址：/api/v1/materials/{material_id}
    请求方式：GET
    """
    try:
        material = Material.query.get(material_id)
        if not material:
            return jsonify({
                'error': 404,
                'message': '航材不存在',
                'body': {},
                'success': False
            }), 404
        
        material_dict = material.to_dict()
        
        # 获取库存信息
        inventory_items = InventoryItem.query.filter_by(material_id=material_id).all()
        material_dict['inventory_items'] = [item.to_dict() for item in inventory_items]
        
        return jsonify({
            'error': 0,
            'message': '航材详情获取成功',
            'body': material_dict,
            'success': True
        })
        
    except Exception as e:
        return jsonify({
            'error': 500,
            'message': f'获取航材详情失败: {str(e)}',
            'body': {},
            'success': False
        }), 500

@materials_bp.route('/categories', methods=['GET'])
@jwt_required()
def get_categories():
    """
    获取航材分类接口
    功能描述：获取所有航材分类
    入参：无
    返回参数：分类列表
    url地址：/api/v1/materials/categories
    请求方式：GET
    """
    try:
        categories = db.session.query(Material.category).distinct().all()
        category_list = [category[0] for category in categories if category[0]]
        
        return jsonify({
            'error': 0,
            'message': '航材分类获取成功',
            'body': {'categories': category_list},
            'success': True
        })
        
    except Exception as e:
        return jsonify({
            'error': 500,
            'message': f'获取航材分类失败: {str(e)}',
            'body': {},
            'success': False
        }), 500

@materials_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_material_statistics():
    """
    获取航材统计信息接口
    功能描述：获取航材统计数据
    入参：无
    返回参数：统计信息
    url地址：/api/v1/materials/statistics
    请求方式：GET
    """
    try:
        # 总航材数
        total_materials = Material.query.count()
        
        # 按分类统计
        category_stats = db.session.query(
            Material.category,
            db.func.count(Material.id).label('count')
        ).group_by(Material.category).all()
        
        # 按制造商统计
        manufacturer_stats = db.session.query(
            Material.manufacturer,
            db.func.count(Material.id).label('count')
        ).filter(Material.manufacturer != '').group_by(Material.manufacturer).all()
        
        statistics = {
            'total_materials': total_materials,
            'category_distribution': [
                {'category': stat.category, 'count': stat.count}
                for stat in category_stats
            ],
            'manufacturer_distribution': [
                {'manufacturer': stat.manufacturer, 'count': stat.count}
                for stat in manufacturer_stats
            ]
        }
        
        return jsonify({
            'error': 0,
            'message': '航材统计信息获取成功',
            'body': statistics,
            'success': True
        })
        
    except Exception as e:
        return jsonify({
            'error': 500,
            'message': f'获取航材统计信息失败: {str(e)}',
            'body': {},
            'success': False
        }), 500