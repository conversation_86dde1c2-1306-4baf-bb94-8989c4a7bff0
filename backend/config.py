#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 应用配置文件
版本: 1.0
创建时间: 2025-01-13

包含开发、测试、生产环境的所有配置参数
"""

import os
from datetime import timedelta

class Config:
    """
    基础配置类
    包含所有环境通用的配置项
    """
    
    # 应用基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'aviation-platform-secret-key-2025'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///aviation_platform.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 3600,  # 连接池回收时间
        'pool_pre_ping': True,  # 连接检查
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-aviation-2025'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)  # 访问令牌24小时有效
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)  # 刷新令牌30天有效
    JWT_ALGORITHM = 'HS256'
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB最大文件大小
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # 邮件配置
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or ''
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or ''
    
    # Redis配置（可选，用于缓存）
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    MAX_ITEMS_PER_PAGE = 100
    
    # 业务配置
    AOG_RESPONSE_TIME_LIMIT = 120  # AOG响应时间限制（分钟）
    DEFAULT_CURRENCY = 'CNY'  # 默认货币
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'

class DevelopmentConfig(Config):
    """
    开发环境配置
    """
    DEBUG = True
    TESTING = False
    
    # 开发环境数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dev_aviation.db')
    
    # 开发环境JWT配置（较短的过期时间便于测试）
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=8)

class TestingConfig(Config):
    """
    测试环境配置
    """
    DEBUG = True
    TESTING = True
    
    # 测试数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # 测试环境JWT配置
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=30)
    
    # 禁用CSRF保护以便测试
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """
    生产环境配置
    """
    DEBUG = False
    TESTING = False
    
    # 生产环境必须从环境变量获取数据库URL
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    
    if SQLALCHEMY_DATABASE_URI and SQLALCHEMY_DATABASE_URI.startswith('postgres://'):
        SQLALCHEMY_DATABASE_URI = SQLALCHEMY_DATABASE_URI.replace('postgres://', 'postgresql://', 1)
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 日志级别
    LOG_LEVEL = 'WARNING'

# 配置字典，根据环境变量选择配置
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# 获取当前配置
def get_config():
    """
    获取当前环境配置
    
    Returns:
        Config: 配置类实例
    """
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])

# 当前使用的配置
Config = get_config()