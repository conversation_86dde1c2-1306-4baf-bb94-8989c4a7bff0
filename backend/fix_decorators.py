#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量修复装饰器冲突的脚本
"""

import os
import re

def fix_decorator_conflicts():
    """
    修复所有路由文件中的装饰器冲突
    """
    routes_dir = '/Users/<USER>/Documents/AIPro/Demo4/backend/routes'
    
    # 需要修复的文件列表
    files_to_fix = [
        'aog.py',
        'maintenance.py', 
        'demands.py',
        'notifications.py',
        'quality.py',
        'inventory.py'
    ]
    
    for filename in files_to_fix:
        filepath = os.path.join(routes_dir, filename)
        if os.path.exists(filepath):
            print(f"正在修复 {filename}...")
            
            # 读取文件内容
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除validate_pagination装饰器的导入
            content = re.sub(
                r'from utils\.decorators import ([^,]+,\s*)?validate_pagination(,\s*[^,]+)*',
                lambda m: f'from utils.decorators import {m.group(1) or ""}{m.group(2) or ""}',
                content
            )
            
            # 移除validate_pagination装饰器的导入（简化版本）
            content = content.replace(', validate_pagination', '')
            content = content.replace('validate_pagination, ', '')
            
            # 移除@validate_pagination装饰器
            content = re.sub(r'@validate_pagination\s*\n', '', content)
            
            # 写回文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"已修复 {filename}")
    
    print("所有装饰器冲突已修复!")

if __name__ == '__main__':
    fix_decorator_conflicts()