#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 工具函数集合
版本: 1.0
创建时间: 2025-01-13

包含验证器、装饰器、响应处理等通用工具函数
"""

import re
import functools
from datetime import datetime
from flask import request, jsonify

def validate_email(email):
    """
    验证邮箱格式
    
    Args:
        email: 邮箱字符串
        
    Returns:
        bool: 是否为有效邮箱格式
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """
    验证密码强度
    
    Args:
        password: 密码字符串
        
    Returns:
        bool: 是否符合密码要求
    """
    if len(password) < 6:
        return False
    
    # 至少包含一个字母和一个数字
    has_letter = re.search(r'[a-zA-Z]', password) is not None
    has_digit = re.search(r'\d', password) is not None
    
    return has_letter and has_digit

def validate_phone(phone):
    """
    验证手机号格式
    
    Args:
        phone: 手机号字符串
        
    Returns:
        bool: 是否为有效手机号
    """
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None

def validate_part_number(part_number):
    """
    验证零件号格式
    
    Args:
        part_number: 零件号字符串
        
    Returns:
        bool: 是否为有效零件号格式
    """
    if not part_number or len(part_number) < 3:
        return False
    
    # 零件号通常包含字母、数字、连字符
    pattern = r'^[A-Za-z0-9\-]{3,50}$'
    return re.match(pattern, part_number) is not None

def generate_number(prefix, sequence_number, date_suffix=True):
    """
    生成业务编号
    
    Args:
        prefix: 前缀
        sequence_number: 序列号
        date_suffix: 是否添加日期后缀
        
    Returns:
        str: 生成的业务编号
    """
    if date_suffix:
        date_str = datetime.now().strftime('%Y%m%d')
        return f"{prefix}-{date_str}-{sequence_number:06d}"
    else:
        return f"{prefix}-{sequence_number:06d}"

def format_currency(amount, currency='CNY'):
    """
    格式化货币金额
    
    Args:
        amount: 金额
        currency: 货币类型
        
    Returns:
        str: 格式化后的货币字符串
    """
    if amount is None:
        return '0.00'
    
    currency_symbols = {
        'CNY': '¥',
        'USD': '$',
        'EUR': '€'
    }
    
    symbol = currency_symbols.get(currency, currency)
    return f"{symbol}{amount:,.2f}"

def calculate_date_diff(start_date, end_date):
    """
    计算日期差异
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        dict: 包含天数、小时数、分钟数的字典
    """
    if not start_date or not end_date:
        return {'days': 0, 'hours': 0, 'minutes': 0}
    
    diff = end_date - start_date
    days = diff.days
    hours, remainder = divmod(diff.seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    
    return {
        'days': days,
        'hours': hours,
        'minutes': minutes,
        'total_hours': round(diff.total_seconds() / 3600, 2)
    }

def safe_cast(value, target_type, default=None):
    """
    安全类型转换
    
    Args:
        value: 要转换的值
        target_type: 目标类型
        default: 默认值
        
    Returns:
        转换后的值或默认值
    """
    try:
        if value is None:
            return default
        return target_type(value)
    except (ValueError, TypeError):
        return default

def paginate_query(query, page=1, size=20, max_size=100):
    """
    分页查询辅助函数
    
    Args:
        query: SQLAlchemy查询对象
        page: 页码
        size: 每页数量
        max_size: 最大每页数量
        
    Returns:
        tuple: (分页对象, 分页信息字典)
    """
    # 限制每页最大数量
    size = min(size, max_size)
    
    # 执行分页查询
    pagination = query.paginate(
        page=page, 
        per_page=size, 
        error_out=False
    )
    
    # 构建分页信息
    pagination_info = {
        'page': page,
        'size': size,
        'total': pagination.total,
        'pages': pagination.pages,
        'has_prev': pagination.has_prev,
        'has_next': pagination.has_next,
        'prev_num': pagination.prev_num,
        'next_num': pagination.next_num
    }
    
    return pagination, pagination_info

def filter_dict(data, allowed_fields):
    """
    过滤字典，只保留允许的字段
    
    Args:
        data: 原始字典
        allowed_fields: 允许的字段列表
        
    Returns:
        dict: 过滤后的字典
    """
    return {key: value for key, value in data.items() if key in allowed_fields}

def merge_dict(dict1, dict2, overwrite=True):
    """
    合并字典
    
    Args:
        dict1: 基础字典
        dict2: 要合并的字典
        overwrite: 是否覆盖重复键
        
    Returns:
        dict: 合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if overwrite or key not in result:
            result[key] = value
    
    return result

def clean_string(text, max_length=None):
    """
    清理字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        
    Returns:
        str: 清理后的字符串
    """
    if not text:
        return ''
    
    # 去除首尾空格
    cleaned = text.strip()
    
    # 限制长度
    if max_length and len(cleaned) > max_length:
        cleaned = cleaned[:max_length]
    
    return cleaned

def is_valid_enum(value, enum_values):
    """
    验证枚举值
    
    Args:
        value: 要验证的值
        enum_values: 有效枚举值列表
        
    Returns:
        bool: 是否为有效枚举值
    """
    return value in enum_values

def get_client_ip():
    """
    获取客户端IP地址
    
    Returns:
        str: 客户端IP地址
    """
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def generate_order_number():
    """
    生成订单号
    
    Returns:
        str: 订单号
    """
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(1000, 9999)
    return f"ORD{timestamp}{random_num}"

def generate_demand_number():
    """
    生成需求编号
    
    Returns:
        str: 需求编号
    """
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(100, 999)
    return f"DMD{timestamp}{random_num}"

def generate_work_order_number():
    """
    生成工单号
    
    Returns:
        str: 工单号
    """
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(100, 999)
    return f"WO{timestamp}{random_num}"

def generate_aog_case_number():
    """
    生成AOG案例编号
    
    Returns:
        str: AOG案例编号
    """
    import random
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_num = random.randint(100, 999)
    return f"AOG{timestamp}{random_num}"

def calculate_aog_response_time(created_at, response_time):
    """
    计算AOG响应时间（分钟）
    
    Args:
        created_at: 创建时间
        response_time: 响应时间
        
    Returns:
        int: 响应时间（分钟）
    """
    if not created_at or not response_time:
        return 0
    
    diff = response_time - created_at
    return int(diff.total_seconds() / 60)

def is_aog_response_overdue(created_at, response_limit_minutes=120):
    """
    判断AOG响应是否超时
    
    Args:
        created_at: 创建时间
        response_limit_minutes: 响应时间限制（分钟）
        
    Returns:
        bool: 是否超时
    """
    if not created_at:
        return False
    
    now = datetime.utcnow()
    diff_minutes = (now - created_at).total_seconds() / 60
    return diff_minutes > response_limit_minutes