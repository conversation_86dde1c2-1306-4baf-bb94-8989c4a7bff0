#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 装饰器函数
版本: 1.0
创建时间: 2025-01-13

包含各种装饰器函数，用于请求验证、权限检查等
"""

import functools
from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt

from models import User

def validate_json(required_fields=None, optional_fields=None):
    """
    验证JSON请求体装饰器
    
    Args:
        required_fields: 必需字段列表
        optional_fields: 可选字段列表
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查Content-Type
            if not request.is_json:
                return jsonify({
                    'error': 400,
                    'message': '请求必须是JSON格式',
                    'body': {},
                    'success': False
                }), 400
            
            # 获取JSON数据
            try:
                data = request.get_json()
                if data is None:
                    return jsonify({
                        'error': 400,
                        'message': '无效的JSON数据',
                        'body': {},
                        'success': False
                    }), 400
            except Exception:
                return jsonify({
                    'error': 400,
                    'message': 'JSON解析失败',
                    'body': {},
                    'success': False
                }), 400
            
            # 验证必需字段
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data or data[field] is None or str(data[field]).strip() == '':
                        missing_fields.append(field)
                
                if missing_fields:
                    return jsonify({
                        'error': 400,
                        'message': f'缺少必需字段: {", ".join(missing_fields)}',
                        'body': {'missing_fields': missing_fields},
                        'success': False
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_roles(allowed_roles):
    """
    角色权限验证装饰器
    
    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            if user.user_type not in allowed_roles:
                return jsonify({
                    'error': 403,
                    'message': '权限不足',
                    'body': {'required_roles': allowed_roles, 'user_role': user.user_type},
                    'success': False
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_admin(f):
    """
    要求管理员权限的装饰器
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or user.user_type != 'admin':
            return jsonify({
                'error': 403,
                'message': '需要管理员权限',
                'body': {},
                'success': False
            }), 403
        
        return f(*args, **kwargs)
    return decorated_function

def require_active_user(f):
    """
    要求活跃用户状态的装饰器
    """
    @functools.wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'error': 404,
                'message': '用户不存在',
                'body': {},
                'success': False
            }), 404
        
        if user.status != 'active':
            return jsonify({
                'error': 403,
                'message': '账户已被禁用',
                'body': {'user_status': user.status},
                'success': False
            }), 403
        
        return f(*args, **kwargs)
    return decorated_function


def log_api_call(f):
    """
    API调用日志装饰器
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        from datetime import datetime
        import logging
        
        # 记录请求信息
        logger = logging.getLogger(__name__)
        logger.info(f"API调用: {request.method} {request.path} - 客户端IP: {request.remote_addr}")
        
        start_time = datetime.now()
        
        try:
            # 执行原函数
            result = f(*args, **kwargs)
            
            # 记录成功信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"API成功: {request.method} {request.path} - 耗时: {duration:.3f}s")
            
            return result
            
        except Exception as e:
            # 记录错误信息
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"API错误: {request.method} {request.path} - 耗时: {duration:.3f}s - 错误: {str(e)}")
            raise
    
    return decorated_function

def rate_limit(max_requests=100, window_seconds=3600):
    """
    简单的速率限制装饰器
    注意: 这是一个简单实现，生产环境建议使用Redis等外部存储
    
    Args:
        max_requests: 最大请求数
        window_seconds: 时间窗口（秒）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现基于IP或用户的速率限制
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def cache_response(timeout=300):
    """
    响应缓存装饰器
    注意: 这是一个简单实现，生产环境建议使用Redis等缓存系统
    
    Args:
        timeout: 缓存超时时间（秒）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 这里可以实现响应缓存逻辑
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_ownership(resource_param='id', user_field='user_id'):
    """
    资源所有权验证装饰器
    验证当前用户是否拥有指定资源
    
    Args:
        resource_param: 资源ID参数名
        user_field: 资源中的用户字段名
    """
    def decorator(f):
        @functools.wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            current_user = User.query.get(current_user_id)
            
            if not current_user:
                return jsonify({
                    'error': 404,
                    'message': '用户不存在',
                    'body': {},
                    'success': False
                }), 404
            
            # 管理员可以访问所有资源
            if current_user.user_type == 'admin':
                return f(*args, **kwargs)
            
            # 这里需要根据具体的资源类型实现所有权检查
            # 简单起见，这里只是一个占位符
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def validate_file_upload(allowed_extensions=None, max_size=None):
    """
    文件上传验证装饰器
    
    Args:
        allowed_extensions: 允许的文件扩展名集合
        max_size: 最大文件大小（字节）
    """
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            if 'file' not in request.files:
                return jsonify({
                    'error': 400,
                    'message': '没有文件被上传',
                    'body': {},
                    'success': False
                }), 400
            
            file = request.files['file']
            
            if file.filename == '':
                return jsonify({
                    'error': 400,
                    'message': '没有选择文件',
                    'body': {},
                    'success': False
                }), 400
            
            # 检查文件扩展名
            if allowed_extensions:
                ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
                if ext not in allowed_extensions:
                    return jsonify({
                        'error': 400,
                        'message': f'不支持的文件类型，允许的类型: {", ".join(allowed_extensions)}',
                        'body': {'allowed_extensions': list(allowed_extensions)},
                        'success': False
                    }), 400
            
            # 检查文件大小
            if max_size:
                file.seek(0, 2)  # 移动到文件末尾
                file_size = file.tell()
                file.seek(0)  # 重置到文件开头
                
                if file_size > max_size:
                    return jsonify({
                        'error': 400,
                        'message': f'文件太大，最大允许 {max_size} 字节',
                        'body': {'max_size': max_size, 'file_size': file_size},
                        'success': False
                    }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator