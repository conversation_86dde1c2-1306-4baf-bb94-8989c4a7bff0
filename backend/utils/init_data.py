#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
航材共享保障平台 - 数据库初始化脚本
版本: 1.0
创建时间: 2025-01-13

初始化数据库结构和基础数据
"""

from datetime import datetime, timedelta
import random
from decimal import Decimal

from models import (
    db, User, Material, InventoryItem, Order, OrderItem, 
    Certificate, WorkOrder, AOGCase, Demand, Notification,
    WorkflowInstance, LogisticsInfo
)
from utils.validators import (
    generate_order_number, generate_demand_number, 
    generate_work_order_number, generate_aog_case_number
)

def init_database():
    """
    初始化数据库数据
    创建基础用户、航材、库存等测试数据
    """
    print("开始初始化数据库...")
    
    # 检查是否已经有足够的数据，如果有则跳过初始化
    if User.query.count() >= 4:
        print("✓ 数据库已经初始化，跳过重复初始化")
        return
    
    try:
        # 创建管理员用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                user_type='admin',
                company_name='系统管理',
                real_name='系统管理员',
                phone='13800138000',
                status='active'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            print("✓ 创建管理员用户")
        
        # 创建测试用户
        test_users = [
            {
                'username': 'airline_user',
                'email': '<EMAIL>',
                'password': 'test123',
                'user_type': 'airline',
                'company_name': '中国航空公司',
                'real_name': '航空采购员',
                'phone': '13801138001'
            },
            {
                'username': 'supplier_user',
                'email': '<EMAIL>',
                'password': 'test123',
                'user_type': 'supplier',
                'company_name': '中航材集团',
                'real_name': '航材供应商',
                'phone': '13801138002'
            },
            {
                'username': 'maintenance_user',
                'email': '<EMAIL>',
                'password': 'test123',
                'user_type': 'maintenance',
                'company_name': '维修技术公司',
                'real_name': '维修工程师',
                'phone': '13801138003'
            }
        ]
        
        for user_data in test_users:
            existing_user = User.query.filter_by(username=user_data['username']).first()
            if not existing_user:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    user_type=user_data['user_type'],
                    company_name=user_data['company_name'],
                    real_name=user_data['real_name'],
                    phone=user_data['phone'],
                    status='active'
                )
                user.set_password(user_data['password'])
                db.session.add(user)
        
        print("✓ 创建测试用户")
        
        # 创建航材数据
        materials_data = [
            {
                'part_number': 'CFM56-7B26-001',
                'part_name': 'CFM56发动机高压压气机叶片',
                'category': '发动机部件',
                'manufacturer': 'CFM International',
                'aircraft_type': 'B737-800',
                'description': '高压压气机第一级叶片，适用于CFM56-7B系列发动机',
                'unit': 'PCS',
                'weight': 2.5,
                'shelf_life_months': 120
            },
            {
                'part_number': 'A320-MLG-001',
                'part_name': 'A320主起落架组件',
                'category': '起落架',
                'manufacturer': 'Safran Landing Systems',
                'aircraft_type': 'A320-200',
                'description': 'A320系列主起落架总成',
                'unit': 'SET',
                'weight': 850.0,
                'shelf_life_months': 240
            },
            {
                'part_number': 'HYD-PUMP-001',
                'part_name': '液压泵组件',
                'category': '液压系统',
                'manufacturer': 'Parker Aerospace',
                'aircraft_type': 'A330-300',
                'description': '主液压系统泵组件',
                'unit': 'PCS',
                'weight': 45.5,
                'shelf_life_months': 60
            },
            {
                'part_number': 'TIRE-BRAKE-001',
                'part_name': '轮胎刹车组件',
                'category': '轮胎刹车',
                'manufacturer': 'Honeywell',
                'aircraft_type': 'B777-300',
                'description': '主起落架轮胎刹车系统',
                'unit': 'SET',
                'weight': 120.0,
                'shelf_life_months': 36
            },
            {
                'part_number': 'AVIONICS-001',
                'part_name': '航电显示器',
                'category': '航电设备',
                'manufacturer': 'Rockwell Collins',
                'aircraft_type': 'A350-900',
                'description': '主显示器单元',
                'unit': 'PCS',
                'weight': 8.5,
                'shelf_life_months': 84
            }
        ]
        
        for material_data in materials_data:
            existing_material = Material.query.filter_by(
                part_number=material_data['part_number']
            ).first()
            if not existing_material:
                material = Material(**material_data)
                material.set_specifications({
                    'technical_specs': '符合FAA/EASA适航标准',
                    'storage_temp': '-20°C to +60°C',
                    'humidity': '≤85% RH'
                })
                db.session.add(material)
        
        print("✓ 创建航材数据")
        
        # 提交用户和航材数据
        db.session.commit()
        
        # 创建库存数据
        materials = Material.query.all()
        supplier = User.query.filter_by(user_type='supplier').first()
        
        locations = ['北京仓库', '上海仓库', '广州仓库', '成都仓库', '西安仓库']
        
        for material in materials:
            for i, location in enumerate(locations[:3]):  # 每个航材在3个仓库有库存
                stock_qty = random.randint(5, 50)
                safety_qty = random.randint(2, 10)
                unit_price = Decimal(str(random.randint(10000, 500000)))
                
                inventory = InventoryItem(
                    material_id=material.id,
                    location=f'{location}-{chr(65+i)}区-{random.randint(1,99):03d}',
                    current_stock=stock_qty,
                    safety_stock=safety_qty,
                    unit_price=unit_price,
                    batch_number=f'BATCH{datetime.now().strftime("%Y%m")}{random.randint(100,999)}',
                    supplier_id=supplier.id if supplier else None
                )
                inventory.update_status()
                db.session.add(inventory)
        
        print("✓ 创建库存数据")
        
        # 创建订单数据
        airline_user = User.query.filter_by(user_type='airline').first()
        supplier_user = User.query.filter_by(user_type='supplier').first()
        
        if airline_user and supplier_user:
            for i in range(10):  # 创建10个示例订单
                order = Order(
                    order_number=generate_order_number(),
                    buyer_id=airline_user.id,
                    supplier_id=supplier_user.id,
                    status=random.choice(['pending', 'confirmed', 'processing', 'shipping', 'completed']),
                    priority=random.choice(['normal', 'high', 'aog']),
                    delivery_address=f'北京首都机场T{random.randint(1,3)}航站楼',
                    delivery_date=datetime.utcnow() + timedelta(days=random.randint(1, 30)),
                    notes='测试订单数据'
                )
                db.session.add(order)
                db.session.flush()  # 获取订单ID
                
                # 添加订单项目
                num_items = random.randint(1, 3)
                selected_materials = random.sample(materials, min(num_items, len(materials)))
                
                for material in selected_materials:
                    quantity = random.randint(1, 5)
                    unit_price = Decimal(str(random.randint(10000, 200000)))
                    
                    order_item = OrderItem(
                        order_id=order.id,
                        material_id=material.id,
                        quantity=quantity,
                        unit_price=unit_price,
                        condition_code=random.choice(['NE', 'OH', 'SV', 'AR'])
                    )
                    order_item.calculate_subtotal()
                    db.session.add(order_item)
                
                order.calculate_total()
        
        print("✓ 创建订单数据")
        
        # 创建证书数据
        certificate_types = ['8130-3', 'caac', 'faa', 'easa']
        authorities = ['CAAC', 'FAA', 'EASA', 'Transport Canada']
        
        for material in materials[:3]:  # 为前3个航材创建证书
            for cert_type in certificate_types[:2]:  # 每个航材2个证书
                certificate = Certificate(
                    certificate_number=f'{cert_type.upper()}-{datetime.now().strftime("%Y")}-{random.randint(1000,9999)}',
                    material_id=material.id,
                    certificate_type=cert_type,
                    issuing_authority=random.choice(authorities),
                    issue_date=datetime.now().date() - timedelta(days=random.randint(30, 365)),
                    expiry_date=datetime.now().date() + timedelta(days=random.randint(365, 1095)),
                    status='valid',
                    verification_status=True
                )
                certificate.update_status()
                db.session.add(certificate)
        
        print("✓ 创建证书数据")
        
        # 创建AOG案例
        maintenance_user = User.query.filter_by(user_type='maintenance').first()
        aircraft_types = ['A320-200', 'B737-800', 'A330-300', 'B777-300', 'A350-900']
        locations = ['北京首都机场', '上海浦东机场', '广州白云机场', '成都双流机场', '深圳宝安机场']
        
        for i in range(5):  # 创建5个AOG案例
            case_number = generate_aog_case_number()
            aircraft_tail = f'B-{random.randint(1000, 9999)}'
            
            aog_case = AOGCase(
                case_number=case_number,
                aircraft_tail=aircraft_tail,
                aircraft_type=random.choice(aircraft_types),
                location=random.choice(locations),
                priority=random.choice(['critical', 'high', 'medium']),
                status=random.choice(['new', 'responding', 'in_progress', 'resolved']),
                fault_title=f'飞机{aircraft_tail}故障报告',
                fault_description='发动机在巡航阶段出现异常振动，需要紧急检查和维修',
                customer_id=airline_user.id if airline_user else 1,
                assigned_team_id=maintenance_user.id if maintenance_user else None,
                contact_name='紧急联系人',
                contact_phone='13800138000',
                contact_email='<EMAIL>',
                created_at=datetime.utcnow() - timedelta(hours=random.randint(1, 72))
            )
            
            if aog_case.status != 'new':
                aog_case.response_time = aog_case.created_at + timedelta(minutes=random.randint(30, 180))
            
            if aog_case.status == 'resolved':
                aog_case.resolution_time = aog_case.created_at + timedelta(hours=random.randint(4, 48))
                aog_case.resolution_notes = '故障已修复，飞机已恢复正常运行'
            
            db.session.add(aog_case)
        
        print("✓ 创建AOG案例数据")
        
        # 创建维修工单
        for i in range(8):  # 创建8个维修工单
            work_order = WorkOrder(
                work_order_number=generate_work_order_number(),
                aircraft_tail=f'B-{random.randint(1000, 9999)}',
                aircraft_type=random.choice(aircraft_types),
                priority=random.choice(['aog', 'high', 'normal', 'low']),
                status=random.choice(['new', 'in_progress', 'waiting_parts', 'completed']),
                fault_title='定期维修检查',
                fault_description='按照维修计划进行的定期检查和保养',
                station=random.choice(['北京维修基地', '上海维修中心', '广州维修站']),
                assigned_technician_id=maintenance_user.id if maintenance_user else None,
                estimated_hours=random.randint(4, 48),
                progress=random.randint(0, 100),
                start_time=datetime.utcnow() - timedelta(hours=random.randint(1, 48)),
                estimated_completion=datetime.utcnow() + timedelta(hours=random.randint(4, 72))
            )
            db.session.add(work_order)
        
        print("✓ 创建维修工单数据")
        
        # 创建需求数据
        for i in range(6):  # 创建6个需求
            demand = Demand(
                demand_number=generate_demand_number(),
                requester_id=airline_user.id if airline_user else 1,
                type=random.choice(['turnaround', 'consumable', 'maintenance', 'aog']),
                priority=random.choice(['aog', 'high', 'normal', 'low']),
                status=random.choice(['published', 'matched', 'negotiating', 'confirmed']),
                material_name=random.choice(materials).part_name,
                part_number=random.choice(materials).part_number,
                aircraft_type=random.choice(aircraft_types),
                quantity=random.randint(1, 10),
                description='紧急需要航材进行维修',
                delivery_location=random.choice(locations),
                delivery_time=datetime.utcnow() + timedelta(days=random.randint(1, 15)),
                budget_range='10万-50万',
                quality_requirements='必须具有有效适航证书',
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            demand.set_contact_info({
                'name': '采购联系人',
                'phone': '13800138000',
                'email': '<EMAIL>'
            })
            db.session.add(demand)
        
        print("✓ 创建需求数据")
        
        # 创建通知数据
        all_users = User.query.all()
        notification_types = ['order', 'aog', 'logistics', 'quality', 'supplier', 'system']
        
        for user in all_users:
            for i in range(3):  # 每个用户3条通知
                notification = Notification(
                    user_id=user.id,
                    type=random.choice(notification_types),
                    title=f'系统通知 - {random.choice(["订单更新", "AOG响应", "库存预警", "证书到期"])}',
                    content='这是一条测试通知内容，用于演示系统通知功能。',
                    priority=random.choice(['low', 'normal', 'high', 'urgent']),
                    is_read=random.choice([True, False]),
                    created_at=datetime.utcnow() - timedelta(hours=random.randint(1, 168))
                )
                if notification.is_read:
                    notification.read_at = notification.created_at + timedelta(minutes=random.randint(5, 60))
                db.session.add(notification)
        
        print("✓ 创建通知数据")
        
        # 提交所有数据
        db.session.commit()
        print("✅ 数据库初始化完成！")
        
        # 打印统计信息
        print("\n📊 数据统计:")
        print(f"  用户数量: {User.query.count()}")
        print(f"  航材数量: {Material.query.count()}")
        print(f"  库存项目: {InventoryItem.query.count()}")
        print(f"  订单数量: {Order.query.count()}")
        print(f"  证书数量: {Certificate.query.count()}")
        print(f"  AOG案例: {AOGCase.query.count()}")
        print(f"  维修工单: {WorkOrder.query.count()}")
        print(f"  需求数量: {Demand.query.count()}")
        print(f"  通知数量: {Notification.query.count()}")
        
        print("\n🔑 默认登录信息:")
        print("  管理员: admin / admin123")
        print("  航空公司: airline_user / test123")
        print("  供应商: supplier_user / test123")
        print("  维修企业: maintenance_user / test123")
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 数据库初始化失败: {str(e)}")
        raise

def clear_database():
    """
    清空数据库数据（慎用！）
    """
    print("⚠️  正在清空数据库...")
    
    try:
        # 按依赖关系顺序删除数据
        tables_to_clear = [
            'notifications',
            'workflow_tasks', 
            'workflow_instances',
            'logistics_info',
            'demand_responses',
            'demands',
            'labor_records',
            'work_orders',
            'aog_cases',
            'certificates',
            'order_items',
            'orders',
            'inventory_items',
            'materials',
            'users'
        ]
        
        for table_name in tables_to_clear:
            db.session.execute(f'DELETE FROM {table_name}')
        
        db.session.commit()
        print("✅ 数据库已清空")
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 清空数据库失败: {str(e)}")
        raise

if __name__ == '__main__':
    # 如果直接运行此脚本，初始化数据库
    from app import app
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        
        # 初始化数据
        init_database()