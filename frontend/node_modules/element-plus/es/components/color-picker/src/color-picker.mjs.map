{"version": 3, "file": "color-picker.mjs", "sources": ["../../../../../../packages/components/color-picker/src/color-picker.ts"], "sourcesContent": ["import { isNil } from 'lodash-unified'\nimport { buildProps, definePropType, isString } from '@element-plus/utils'\nimport {\n  useAriaProps,\n  useEmptyValuesProps,\n  useSizeProp,\n} from '@element-plus/hooks'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type {\n  ComputedRef,\n  ExtractPropTypes,\n  InjectionKey,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type ColorPicker from './color-picker.vue'\n\nexport const colorPickerProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<string | null>(String),\n    default: undefined,\n  },\n  /**\n   * @description ColorPicker id\n   */\n  id: String,\n  /**\n   * @description whether to display the alpha slider\n   */\n  showAlpha: Boolean,\n  /**\n   * @description color format of v-model\n   */\n  colorFormat: String,\n  /**\n   * @description whether to disable the ColorPicker\n   */\n  disabled: Boolean,\n  /**\n   * @description size of ColorPicker\n   */\n  size: useSizeProp,\n  /**\n   * @description custom class name for ColorPicker's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description ColorPicker tabindex\n   */\n  tabindex: {\n    type: [String, Number],\n    default: 0,\n  },\n  /**\n   * @description whether color-picker popper is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description predefined color options\n   */\n  predefine: {\n    type: definePropType<string[]>(Array),\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport const colorPickerEmits = {\n  [UPDATE_MODEL_EVENT]: (val: string | null) => isString(val) || isNil(val),\n  [CHANGE_EVENT]: (val: string | null) => isString(val) || isNil(val),\n  activeChange: (val: string | null) => isString(val) || isNil(val),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n}\n\nexport type ColorPickerProps = ExtractPropTypes<typeof colorPickerProps>\nexport type ColorPickerPropsPublic = __ExtractPublicPropTypes<\n  typeof colorPickerProps\n>\nexport type ColorPickerEmits = typeof colorPickerEmits\nexport type ColorPickerInstance = InstanceType<typeof ColorPicker> & unknown\n\nexport interface ColorPickerContext {\n  currentColor: ComputedRef<string>\n}\n\nexport const colorPickerContextKey: InjectionKey<ColorPickerContext> = Symbol(\n  'colorPickerContextKey'\n)\n"], "names": [], "mappings": ";;;;;;;;;AASY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,UAAU,EAAE,sBAAsB,CAAC,UAAU;AAC/C,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,mBAAmB;AACxB,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG;AAChC,EAAE,CAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AAC5D,EAAE,CAAC,YAAY,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AACtD,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;AACpD,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE;AACU,MAAC,qBAAqB,GAAG,MAAM,CAAC,uBAAuB;;;;"}