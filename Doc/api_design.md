# 航材共享保障平台 API 设计文档

## 版本信息
| 版本号 | 日期 | 修改人 | 修改内容 |
|--------|------|--------|----------|
| v1.0   | 2024-12-19 | 系统设计师 | 初版API设计 |
| v1.1   | 2025-01-13 | 开发团队 | 增加前端实际调用的API |

## 1. 认证管理 API

### 1.1 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "userType": "airline"
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_string",
    "refresh_token": "refresh_token_string",
    "user": {
      "id": 1,
      "username": "<EMAIL>",
      "user_type": "airline",
      "status": "active"
    }
  }
}
```

### 1.2 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "refresh_token_string"
}
```

### 1.3 退出登录
```http
POST /auth/logout
Authorization: Bearer <jwt_token>
```

## 2. 航材市场 API

### 2.1 搜索航材
```http
GET /materials/search?keyword=发动机&category=engine&aircraft_type=B737&page=1&size=12
```

**查询参数：**
- `keyword`: 搜索关键词
- `category`: 航材类别 (engine, landing_gear, avionics, hydraulic, wheels_brakes)
- `aircraft_type`: 机型 (A320, A330, B737, B777等)
- `condition`: 航材状态 (new, serviceable, repaired)
- `min_price`: 最低价格
- `max_price`: 最高价格
- `location`: 所在地区 (beijing, shanghai, guangzhou等)
- `page`: 页码
- `size`: 每页数量
- `sort`: 排序方式 (relevance, price_asc, price_desc, created_desc)

**响应：**
```json
{
  "total": 150,
  "page": 1,
  "size": 12,
  "pages": 13,
  "items": [
    {
      "id": 1,
      "name": "发动机高压压气机叶片",
      "part_number": "CFM56-7B-001",
      "description": "适用于CFM56-7B系列发动机",
      "price": 35600,
      "condition": "new",
      "aircraft_type": "B737",
      "category": "engine",
      "location": "北京",
      "image": "/pictures/发动机1.jpg",
      "supplier": {
        "name": "中航材北京公司",
        "rating": 4.8
      },
      "stock": 5
    }
  ]
}
```

### 2.2 获取航材详情
```http
GET /materials/{id}
```

### 2.3 获取航材分类
```http
GET /materials/categories
```

### 2.4 获取制造商列表
```http
GET /materials/manufacturers
```

## 3. 订单管理 API

### 3.1 获取订单列表
```http
GET /orders?status=processing&type=purchase&page=1&size=10
Authorization: Bearer <jwt_token>
```

**查询参数：**
- `status`: 订单状态 (pending, confirmed, processing, shipped, completed, cancelled)
- `type`: 订单类型 (purchase, sale, rental, aog)
- `customer`: 客户/供应商
- `date_from`: 开始日期
- `date_to`: 结束日期
- `page`: 页码
- `size`: 每页数量

**响应：**
```json
{
  "total": 45,
  "page": 1,
  "size": 10,
  "pages": 5,
  "items": [
    {
      "id": 1,
      "order_number": "PO-2025-001",
      "type": "purchase",
      "status": "processing",
      "customer": {
        "name": "中航材北京公司",
        "avatar": ""
      },
      "material_name": "发动机高压压气机叶片",
      "part_number": "CFM56-7B-001",
      "quantity": 2,
      "unit": "件",
      "unit_price": 35600,
      "total_amount": 71200,
      "created_at": "2025-01-10",
      "estimated_delivery": "2025-01-20"
    }
  ]
}
```

### 3.2 获取订单详情
```http
GET /orders/{id}
Authorization: Bearer <jwt_token>
```

### 3.3 创建订单
```http
POST /orders
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "material_id": 1,
  "quantity": 2,
  "unit_price": 35600,
  "delivery_address": "北京市朝阳区",
  "delivery_contact": "张三",
  "delivery_phone": "13800138000",
  "expected_delivery_date": "2025-01-20",
  "notes": "请确保质量"
}
```

### 3.4 更新订单状态
```http
PATCH /orders/{id}/status
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "status": "shipped",
  "tracking_number": "SF1234567890",
  "logistics_company": "顺丰速运",
  "notes": "已发货"
}
```

## 4. 库存管理 API

### 4.1 获取库存列表
```http
GET /inventory?search=发动机&category=engine&status=normal&location=beijing&page=1&size=10
Authorization: Bearer <jwt_token>
```

**查询参数：**
- `search`: 搜索关键词
- `category`: 航材类别
- `status`: 库存状态 (normal, warning, shortage)
- `location`: 仓库位置 (beijing, shanghai, guangzhou)
- `aircraft_type`: 机型
- `page`: 页码
- `size`: 每页数量

**响应：**
```json
{
  "total": 1850,
  "page": 1,
  "size": 10,
  "pages": 185,
  "items": [
    {
      "id": 1,
      "material_info": {
        "name": "发动机高压压气机叶片",
        "part_number": "CFM56-7B-001",
        "aircraft_type": "B737",
        "category": "engine",
        "image": "/pictures/发动机1.jpg"
      },
      "current_stock": 15,
      "safety_stock": 10,
      "location": "beijing",
      "unit_price": 35600,
      "status": "normal",
      "last_updated": "2025-01-12"
    }
  ]
}
```

### 4.2 获取库存详情
```http
GET /inventory/{id}
Authorization: Bearer <jwt_token>
```

### 4.3 入库操作
```http
POST /inventory/inbound
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "material_id": 1,
  "quantity": 10,
  "location": "beijing",
  "unit_price": 35600,
  "supplier_id": 1,
  "notes": "新到货品"
}
```

**响应：**
```json
{
  "transaction_id": "TXN-2025-001",
  "new_stock": 25,
  "message": "入库成功"
}
```

### 4.4 出库操作
```http
POST /inventory/outbound
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "inventory_id": 1,
  "quantity": 2,
  "reason": "销售出库",
  "recipient": "东方航空维修",
  "notes": "按订单出库"
}
```

### 4.5 库存调拨
```http
POST /inventory/transfer
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "inventory_id": 1,
  "quantity": 5,
  "from_location": "beijing",
  "to_location": "shanghai",
  "notes": "库存调拨"
}
```

### 4.6 获取库存统计
```http
GET /inventory/statistics
Authorization: Bearer <jwt_token>
```

**响应：**
```json
{
  "total_items": 1850,
  "warning_items": 23,
  "shortage_items": 8,
  "total_value": 15600000,
  "turnover_rate": 78
}
```

### 4.7 获取库存预警
```http
GET /inventory/warnings?type=shortage
Authorization: Bearer <jwt_token>
```

## 5. 需求发布 API

### 5.1 创建需求
```http
POST /demands
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "type": "purchase",
  "material_name": "发动机高压压气机叶片",
  "part_number": "CFM56-7B-001",
  "category": "engine",
  "aircraft_type": "B737",
  "quantity": 2,
  "acceptable_conditions": ["new", "serviceable"],
  "expected_price": "30000-40000",
  "delivery_location": "beijing",
  "expected_delivery_date": "2025-02-01",
  "urgency": "urgent",
  "description": "急需2件发动机高压压气机叶片",
  "contact_person": "张三",
  "contact_phone": "13800138000",
  "contact_email": "<EMAIL>",
  "attachments": [
    {
      "name": "requirement.pdf",
      "size": 1024000,
      "type": "application/pdf"
    }
  ]
}
```

**响应：**
```json
{
  "demand_id": "DEM-2025-001",
  "message": "需求发布成功"
}
```

### 5.2 保存需求草稿
```http
POST /demands/draft
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### 5.3 获取需求列表
```http
GET /demands?status=active&type=purchase&page=1&size=20
Authorization: Bearer <jwt_token>
```

### 5.4 获取需求详情
```http
GET /demands/{id}
Authorization: Bearer <jwt_token>
```

## 6. 通知管理 API

### 6.1 获取通知列表
```http
GET /notifications?type=order&read=false&page=1&size=20
Authorization: Bearer <jwt_token>
```

**响应：**
```json
{
  "total": 15,
  "unread_count": 8,
  "items": [
    {
      "id": 1,
      "type": "order",
      "title": "新订单待处理",
      "content": "您有一个新的采购订单需要确认",
      "read": false,
      "created_at": "2025-01-13T10:30:00Z",
      "metadata": {
        "order_number": "PO-2025-001",
        "priority": "normal"
      }
    }
  ]
}
```

### 6.2 标记通知已读
```http
PATCH /notifications/{id}/read
Authorization: Bearer <jwt_token>
```

### 6.3 标记所有通知已读
```http
PATCH /notifications/read-all
Authorization: Bearer <jwt_token>
```

## 7. 文件上传 API

### 7.1 上传文件
```http
POST /files/upload
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

{
  "file": "binary_file_data",
  "type": "image",
  "category": "material"
}
```

**响应：**
```json
{
  "file_id": "uuid-string",
  "file_name": "material_image.jpg",
  "file_size": 1024000,
  "file_type": "image/jpeg",
  "file_url": "https://portal.example.com/files/uuid-string.jpg"
}
```

## 8. 统计分析 API

### 8.1 获取工作台统计
```http
GET /statistics/dashboard
Authorization: Bearer <jwt_token>
```

**响应：**
```json
{
  "orders": {
    "pending": 8,
    "processing": 15,
    "completed": 42,
    "total_amount": 1285000
  },
  "inventory": {
    "total_items": 1850,
    "warning_items": 23,
    "shortage_items": 8,
    "total_value": 15600000
  },
  "recent_activities": [
    {
      "id": 1,
      "type": "order",
      "title": "新订单创建",
      "description": "航材需求 - 发动机部件",
      "time": "2小时前"
    }
  ]
}
```

### 8.2 获取市场热门航材
```http
GET /statistics/popular-materials?limit=10&days=30
```

### 8.3 获取价格趋势
```http
GET /statistics/price-trends?material_id=1&days=90
```

## 9. 错误码定义

### 9.1 HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

### 9.2 业务错误码
- `10001`: 用户不存在
- `10002`: 密码错误
- `10003`: 账户已被禁用
- `20001`: 航材不存在
- `20002`: 航材已下架
- `30001`: 库存不足
- `40001`: 订单不存在
- `40002`: 订单状态错误
- `50001`: 需求已过期

## 10. 接口响应格式

### 10.1 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 10.2 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "code": 10001,
  "message": "用户不存在"
}
```

## 11. 认证机制

### 11.1 JWT Token
- **Header**: `Authorization: Bearer <jwt_token>`
- **过期时间**: 2小时
- **刷新机制**: 使用refresh_token刷新

### 11.2 权限控制
- **航空公司**: 可查看市场、创建采购订单、管理库存
- **中航材**: 可发布航材、管理销售订单、查看统计
- **维修企业**: 可查看市场、创建维修需求、管理库存

---

## 总结

本API设计文档涵盖了航材共享保障平台前端应用的所有核心功能，包括：

1. **认证管理**: 用户登录、退出、token刷新
2. **航材市场**: 搜索、浏览、筛选航材资源
3. **订单管理**: 创建、查看、更新订单状态
4. **库存管理**: 入库、出库、调拨、统计分析
5. **需求发布**: 发布采购需求、AOG紧急响应
6. **通知管理**: 消息通知、系统提醒
7. **文件上传**: 图片、文档上传管理
8. **统计分析**: 数据统计、趋势分析

所有API接口采用RESTful设计风格，支持JWT认证，提供统一的响应格式和完善的错误处理机制。
