# 航材共享保障平台 - 开发技术文档

**版本：** v1.0  
**创建日期：** 2025-01-08  
  

---

## 📋 项目概述

### 项目定位
航材共享保障平台是一个**B2B航材共享服务平台**，连接航空公司、航材公司、维修企业和物流企业，实现航材资源的高效共享和配置。

### 核心价值
- **周转件服务**：灵活租赁，降低库存成本40%
- **消耗件保障**：批量采购，提升采购效率60%  
- **AOG紧急响应**：24小时服务，2小时内响应
- **智能匹配**：AI算法，精准匹配供需双方

### 用户角色
- **航空公司采购员**：发布需求、管理订单
- **航材业务员**：管理库存、处理订单
- **维修工程师**：更新维修状态、技术支持
- **物流专员**：跟踪配送、更新状态
- **系统管理员**：系统配置、数据分析

---

## 🏗️ 技术架构设计

### 技术栈
- **前端**：Vue 3 + Element Plus + Vite + Pinia
- **后端**：Python 3.9 + Flask 3.0 + SQLite + SQLAlchemy
- **UI框架**：Tailwind CSS + Lucide Icons
- **工作流**：SpiffWorkflow（审批流程）
- **部署**：Docker + Nginx

### 架构模式
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   后端服务层     │    │   数据存储层     │
│                │    │                │    │                │
│ Vue 3 + Vite   │◄──►│ Flask + SQLAlchemy│◄──►│ SQLite Database │
│ Element Plus   │    │ RESTful API    │    │ File Storage   │
│ Tailwind CSS   │    │ JWT Auth       │    │ Cache Layer    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 🎨 前端设计规范

### 设计系统
```css
:root {
    --primary-blue: #3B82F6;
    --primary-blue-dark: #1D4ED8;
    --success-green: #10B981;
    --warning-orange: #F59E0B;
    --error-red: #EF4444;
    --font-title: 'Inter', sans-serif;
    --font-body: 'Roboto', sans-serif;
}
```

### 组件规范
- **按钮**：12px圆角，渐变背景，悬停动效
- **卡片**：20px圆角，0-12px阴影，悬停上移
- **表单**：实时验证，错误提示，加载状态
- **表格**：斑马纹，悬停高亮，分页组件

### 响应式布局
- **桌面端 (1920px+)**：三栏布局
- **平板端 (768px-1919px)**：两栏布局
- **移动端 (320px-767px)**：单栏布局

---

## 📱 页面结构分析

### 1. 门户首页 (`/`)
**目标用户**：潜在客户、访客
**核心功能**：品牌展示、解决方案介绍、用户注册引导

#### 关键组件
```vue
<template>
  <div id="homepage">
    <!-- 顶部导航 -->
    <HeaderNav />
    
    <!-- 英雄区域 -->
    <HeroSection 
      :stats="platformStats"
      @onGetStarted="navigateToLogin"
    />
    
    <!-- 核心解决方案 -->
    <SolutionsSection :solutions="coreSolutions" />
    
    <!-- 价值主张 -->
    <ValuePropositionSection />
    
    <!-- 服务对象 -->
    <TargetCustomersSection />
    
    <!-- 客户案例 -->
    <SuccessStoriesSection :cases="customerCases" />
    
    <!-- CTA区域 -->
    <CallToActionSection />
    
    <!-- 页脚 -->
    <FooterSection />
  </div>
</template>
```

#### 数据结构
```javascript
// 平台统计数据
platformStats: {
  materialCount: "50万+",
  partnerCount: "500+", 
  availability: "99.8%",
  responseTime: "2小时"
}

// 核心解决方案
coreSolutions: [
  {
    id: 'turnaround',
    title: '周转件服务',
    description: '灵活服务，降低库存成本',
    features: ['智能需求匹配', '质量追溯', '灵活租期', '专业物流'],
    savings: '40%'
  }
]
```

### 2. 登录页面 (`/login`)
**目标用户**：注册用户
**核心功能**：身份验证、用户类型选择

#### 关键组件
```vue
<template>
  <div id="loginpage">
    <LoginForm 
      :userTypes="userTypes"
      @onLogin="handleLogin"
      @onRegister="navigateToRegister"
    />
  </div>
</template>
```

#### 用户类型
```javascript
userTypes: [
  { value: 'admin', label: '系统管理员', icon: 'plane' },
  { value: 'airline', label: '航空公司', icon: 'plane' },
  { value: 'supplier', label: '航材公司', icon: 'building-2' },
  { value: 'maintenance', label: '维修企业', icon: 'wrench' }
]
```

### 3. 主应用界面 (`/app`)
**目标用户**：已登录用户
**核心功能**：业务操作、数据管理

#### 主导航结构
```javascript
mainNavigation: [
  {
    id: 'workspace',
    label: '工作台',
    icon: 'activity',
    component: 'WorkspaceView'
  },
  {
    id: 'marketplace', 
    label: '航材市场',
    icon: 'search',
    component: 'MarketplaceView'
  },
  {
    id: 'orders',
    label: '我的订单', 
    icon: 'shopping-cart',
    component: 'OrdersView'
  },
  {
    id: 'inventory',
    label: '库存管理',
    icon: 'package',
    component: 'InventoryView'
  },
  {
    id: 'maintenance',
    label: '维修管理',
    icon: 'wrench', 
    component: 'MaintenanceView'
  },
  {
    id: 'quality',
    label: '质量管理',
    icon: 'shield-check',
    component: 'QualityView'
  },
  {
    id: 'analytics',
    label: '数据分析',
    icon: 'bar-chart-3',
    component: 'AnalyticsView'
  }
]
```

---

## 🔧 核心功能模块

### 1. 工作台 (Workspace)
**功能**：数据概览、快速操作、消息通知

#### 组件结构
```vue
<template>
  <div class="workspace">
    <!-- 欢迎区域 -->
    <WelcomeBanner :user="currentUser" :alerts="urgentAlerts" />
    
    <!-- 快速操作 -->
    <QuickActions :actions="quickActionItems" />
    
    <!-- 数据概览 -->
    <DataOverview :metrics="dashboardMetrics" />
  </div>
</template>
```

#### 关键数据
```javascript
dashboardMetrics: {
  totalMaterials: 12847,
  activeDemands: 1234, 
  processingOrders: 567,
  monthlyRevenue: 2800000
}
```

### 2. 航材市场 (Marketplace)
**功能**：航材搜索、筛选、询价购买

#### 搜索功能
```vue
<template>
  <div class="marketplace">
    <!-- 搜索表单 -->
    <PartSearchForm 
      :searchCriteria="searchCriteria"
      @onSearch="handleSearch"
    />
    
    <!-- 搜索结果 -->
    <SearchResults 
      :results="searchResults"
      :pagination="pagination"
      @onInquiry="handleInquiry"
      @onPurchase="handlePurchase"
    />
  </div>
</template>
```

#### 搜索条件
```javascript
searchCriteria: {
  oem: ['Honeywell', 'Boeing', 'Airbus'],
  crossReference: ['MCRN', 'NMCRL', 'PMA'],
  conditionCodes: ['NE', 'NS', 'OH', 'SV', 'AR'],
  partNumbers: [],
  quantities: []
}
```

### 3. 订单管理 (Orders)
**功能**：订单创建、跟踪、状态管理

#### 订单状态流
```javascript
orderStatuses: {
  pending: { label: '待确认', color: 'blue' },
  processing: { label: '处理中', color: 'orange' },
  shipping: { label: '运输中', color: 'purple' },
  completed: { label: '已完成', color: 'green' },
  cancelled: { label: '已取消', color: 'red' }
}
```

### 4. 库存管理 (Inventory)
**功能**：库存查询、入库出库、预警管理

#### 库存数据结构
```javascript
inventoryItem: {
  id: 'inv_001',
  partNumber: 'CFM56-7B26-001',
  partName: 'CFM56发动机叶片',
  category: '发动机部件',
  currentStock: 8,
  safetyStock: 5,
  location: '北京仓库-A区-001',
  status: 'normal', // normal, warning, shortage, expired
  unitPrice: 125000,
  lastUpdated: '2025-01-08T10:30:00Z'
}
```

### 5. 维修管理 (Maintenance)
**功能**：维修工单、进度跟踪、技术支持

#### 工单数据结构
```javascript
maintenanceOrder: {
  id: 'MX-2025-001234',
  aircraftTail: 'B-1234',
  aircraftType: '波音737-800',
  priority: 'aog', // aog, high, normal, low
  description: '左发动机CFM56叶片发现裂纹',
  station: '北京维修基地',
  technician: '李工程师',
  startTime: '2025-01-08T08:00:00Z',
  estimatedHours: 8,
  status: 'waiting_parts' // waiting_parts, in_progress, completed
}
```

### 6. 质量管理 (Quality)
**功能**：适航证书、质检记录、合规管理

#### 证书数据结构
```javascript
airworthinessCertificate: {
  id: 'cert_001',
  partNumber: 'CFM56-7B26-001',
  certificateType: '8130-3适航标签',
  issuingAuthority: 'CAAC',
  issueDate: '2025-01-01',
  expiryDate: '2025-12-31',
  status: 'valid', // valid, expiring, expired
  documents: ['cert.pdf', 'test_report.pdf']
}
```

### 7. 数据分析 (Analytics)
**功能**：业务指标、趋势分析、报表生成

#### 分析指标
```javascript
analyticsMetrics: {
  monthlyRevenue: 12500000,
  activeUsers: 1247,
  avgResponseTime: 1.8, // hours
  orderCompletionRate: 96.8 // percentage
}
```

---

## 🎯 特殊功能设计

### 1. 发布需求 (Publish Demand)
**触发方式**：快速操作按钮、工作台入口

#### 表单结构
```javascript
demandForm: {
  basicInfo: {
    type: 'turnaround', // turnaround, consumable, maintenance, aog
    priority: 'normal' // aog, high, normal, low
  },
  materialInfo: {
    name: '',
    partNumber: '',
    aircraftType: '',
    quantity: 1
  },
  requirements: {
    description: '',
    deliveryTime: '',
    deliveryLocation: '',
    qualityRequirements: []
  }
}
```

### 2. 物流跟踪 (Logistics Tracking)
**核心功能**：实时位置、状态更新、预计到达

#### 物流状态
```javascript
logisticsStatuses: [
  { key: 'shipped', label: '已发货', color: 'green' },
  { key: 'in_transit', label: '运输中', color: 'blue' },
  { key: 'arrived', label: '已到达', color: 'purple' },
  { key: 'delivered', label: '已签收', color: 'green' }
]
```

### 3. AOG紧急响应 (Emergency Response)
**核心特点**：24小时服务、优先级处理、专线联系

#### AOG表单
```javascript
aogRequest: {
  aircraftInfo: {
    tailNumber: '',
    aircraftType: '',
    location: ''
  },
  faultInfo: {
    description: '',
    partRequired: '',
    urgencyLevel: 'critical'
  },
  contactInfo: {
    name: '',
    phone: '',
    email: ''
  }
}
```

### 4. 通知中心 (Notification Center)
**功能**：消息管理、实时通知、分类筛选

#### 通知类型
```javascript
notificationTypes: {
  order: { label: '订单通知', icon: 'shopping-cart', color: 'blue' },
  aog: { label: 'AOG紧急', icon: 'alert-triangle', color: 'orange' },
  logistics: { label: '物流更新', icon: 'truck', color: 'green' },
  quality: { label: '质量认证', icon: 'file-text', color: 'purple' },
  supplier: { label: '供应商消息', icon: 'users', color: 'indigo' }
}
```

---

## 🛠️ 后端API设计

### 认证接口
```python
# 用户登录
POST /api/v1/auth/login
{
  "username": "<EMAIL>", 
  "password": "password123",
  "user_type": "airline"
}

# 刷新Token  
POST /api/v1/auth/refresh
{
  "refresh_token": "refresh_token_string"
}
```

### 核心业务接口
```python
# 航材搜索
GET /api/v1/materials/search?q=cfm56&category=engine&page=1&size=20

# 发布需求
POST /api/v1/demands
{
  "type": "turnaround",
  "priority": "normal", 
  "material_info": {...},
  "requirements": {...}
}

# 订单管理
GET /api/v1/orders?status=processing&page=1&size=20
POST /api/v1/orders
PUT /api/v1/orders/{order_id}/status

# 库存管理
GET /api/v1/inventory?category=engine&status=normal
POST /api/v1/inventory/inbound
POST /api/v1/inventory/outbound

# 通知管理
GET /api/v1/notifications?type=order&is_read=false
PATCH /api/v1/notifications/{id}/read
```

---

## 🗄️ 数据库设计

### 核心数据表

#### 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('airline', 'supplier', 'maintenance') NOT NULL,
    company_name VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 航材表 (materials)
```sql
CREATE TABLE materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    part_number VARCHAR(50) UNIQUE NOT NULL,
    part_name VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100),
    aircraft_type VARCHAR(50),
    description TEXT,
    specifications JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 库存表 (inventory)
```sql
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_id INTEGER NOT NULL,
    location VARCHAR(100) NOT NULL,
    current_stock INTEGER NOT NULL DEFAULT 0,
    safety_stock INTEGER NOT NULL DEFAULT 0,
    unit_price DECIMAL(12,2),
    status ENUM('normal', 'warning', 'shortage') DEFAULT 'normal',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (material_id) REFERENCES materials(id)
);
```

#### 订单表 (orders)
```sql
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    buyer_id INTEGER NOT NULL,
    supplier_id INTEGER NOT NULL,
    material_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2),
    total_amount DECIMAL(12,2),
    status ENUM('pending', 'processing', 'shipping', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('aog', 'high', 'normal', 'low') DEFAULT 'normal',
    delivery_address TEXT,
    delivery_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (buyer_id) REFERENCES users(id),
    FOREIGN KEY (supplier_id) REFERENCES users(id),
    FOREIGN KEY (material_id) REFERENCES materials(id)
);
```

---

## 🔄 工作流设计

### 审批流程配置
```python
# 订单审批流程
order_approval_workflow = {
    'name': 'order_approval',
    'steps': [
        {
            'id': 'submit',
            'name': '提交订单',
            'type': 'start',
            'next': 'department_review'
        },
        {
            'id': 'department_review', 
            'name': '部门审核',
            'type': 'approval',
            'approver_role': 'department_manager',
            'conditions': [
                {'field': 'total_amount', 'operator': '>', 'value': 100000, 'next': 'finance_review'},
                {'field': 'total_amount', 'operator': '<=', 'value': 100000, 'next': 'auto_approve'}
            ]
        },
        {
            'id': 'finance_review',
            'name': '财务审核', 
            'type': 'approval',
            'approver_role': 'finance_manager',
            'next': 'final_approve'
        },
        {
            'id': 'auto_approve',
            'name': '自动通过',
            'type': 'auto',
            'next': 'end'
        },
        {
            'id': 'final_approve',
            'name': '最终批准',
            'type': 'end'
        }
    ]
}
```

---

## 📋 开发优先级建议

### Phase 1: 核心功能 (4-6周)
1. **用户认证系统** - 登录、注册、权限管理
2. **航材市场** - 搜索、浏览、基础筛选
3. **订单管理** - 创建、查看、状态更新
4. **库存管理** - 基础增删改查

### Phase 2: 业务流程 (3-4周)  
1. **需求发布** - 表单提交、需求匹配
2. **工作流引擎** - 审批流程、状态流转
3. **通知系统** - 消息推送、通知中心
4. **物流跟踪** - 状态更新、进度显示

### Phase 3: 高级功能 (2-3周)
1. **AOG紧急响应** - 优先级处理、快速通道
2. **数据分析** - 报表生成、趋势分析  
3. **质量管理** - 证书管理、合规检查
4. **维修管理** - 工单系统、进度跟踪

### Phase 4: 优化完善 (2-3周)
1. **性能优化** - 缓存、分页、懒加载
2. **移动端适配** - 响应式设计、触摸优化
3. **安全加固** - 输入验证、权限细化
4. **用户体验** - 动画效果、加载状态

---

## 🔒 安全考虑

### 前端安全
- **输入验证**：所有用户输入进行客户端验证
- **XSS防护**：使用Vue.js内置的模板转义
- **CSRF防护**：API请求携带CSRF Token
- **敏感数据**：避免在前端存储敏感信息

### 后端安全
- **身份认证**：JWT Token + 刷新机制
- **权限控制**：基于角色的访问控制(RBAC)
- **SQL注入**：使用SQLAlchemy ORM参数化查询
- **数据加密**：敏感数据库字段加密存储

---

## 📈 性能优化

### 前端优化
- **代码分割**：按路由进行懒加载
- **图片优化**：WebP格式、延迟加载
- **缓存策略**：静态资源浏览器缓存
- **包大小**：Tree Shaking移除未使用代码

### 后端优化  
- **数据库索引**：为查询字段创建合适索引
- **查询优化**：避免N+1查询，使用连接查询
- **缓存层**：Redis缓存热点数据
- **分页查询**：大数据集使用分页加载

---

## 🚀 部署方案

### 开发环境
```bash
# 前端开发
npm run dev

# 后端开发  
python app.py

# 数据库
sqlite3 database.db
```

### 生产环境
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
      
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=sqlite:///app.db
      - JWT_SECRET_KEY=${JWT_SECRET}
    volumes:
      - ./data:/app/data
      
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
```

---

## 📝 开发注意事项

### 代码规范
- **Vue组件**：使用Composition API，单文件组件
- **Python代码**：遵循PEP 8规范，使用类型提示
- **API文档**：所有接口必须包含完整JSDoc注释
- **错误处理**：统一错误码和错误信息格式

### 测试策略
- **单元测试**：核心业务逻辑100%覆盖
- **集成测试**：API接口功能测试
- **E2E测试**：关键用户路径测试
- **性能测试**：并发访问和响应时间测试

### 文档维护
- **API文档**：与代码同步更新
- **用户手册**：功能更新后及时更新
- **部署文档**：环境配置和部署步骤
- **变更日志**：记录所有功能变更和bug修复

---

*本文档基于HTML原型文件分析生成，为航材共享保障平台的开发提供完整的技术指导。*